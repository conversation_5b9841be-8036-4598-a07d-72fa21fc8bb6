/**
 * Test file to verify refresh token duplicate call fix
 * This test demonstrates that refresh token is only called once even with multiple 401 errors
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import axios from 'axios'
import { createAxiosInstance } from '@/configs/axios'

// Mock dependencies
vi.mock('@/utils/auth', () => ({
  getAccessToken: vi.fn(() => 'expired-token'),
  setAccessToken: vi.fn(),
  clearAuthData: vi.fn(),
  getDeviceInfo: vi.fn(() => ({ userAgent: 'test' })),
}))

vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}))

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/dashboard',
    href: '',
  },
  writable: true,
})

describe('Refresh Token Duplicate Call Fix', () => {
  let axiosInstance: any
  let mockPost: any

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Create fresh axios instance
    axiosInstance = createAxiosInstance()
    
    // Mock axios.post for refresh token endpoint
    mockPost = vi.spyOn(axios, 'post')
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should only call refresh token API once for multiple simultaneous 401 errors', async () => {
    // Mock refresh token success response
    mockPost.mockResolvedValueOnce({
      data: { access_token: 'new-token' }
    })

    // Mock multiple API calls that return 401
    const mockGet = vi.spyOn(axiosInstance, 'get')
    mockGet
      .mockRejectedValueOnce({
        response: { status: 401 },
        config: { headers: {} }
      })
      .mockRejectedValueOnce({
        response: { status: 401 },
        config: { headers: {} }
      })
      .mockRejectedValueOnce({
        response: { status: 401 },
        config: { headers: {} }
      })

    // Make multiple simultaneous requests that will get 401
    const promises = [
      axiosInstance.get('/api/test1').catch(() => {}),
      axiosInstance.get('/api/test2').catch(() => {}),
      axiosInstance.get('/api/test3').catch(() => {}),
    ]

    await Promise.all(promises)

    // Verify refresh token API was called only once
    expect(mockPost).toHaveBeenCalledTimes(1)
    expect(mockPost).toHaveBeenCalledWith(
      'http://localhost:5000/api/identity/refresh-token',
      { deviceInfo: { userAgent: 'test' } },
      { withCredentials: true }
    )
  })

  it('should queue requests during token refresh', async () => {
    let refreshResolve: (value: any) => void
    
    // Mock refresh token with delayed response
    const refreshPromise = new Promise((resolve) => {
      refreshResolve = resolve
    })
    
    mockPost.mockImplementationOnce(() => refreshPromise)

    // Mock successful retry after refresh
    const mockGet = vi.spyOn(axiosInstance, 'get')
    mockGet
      .mockRejectedValueOnce({
        response: { status: 401 },
        config: { headers: {} }
      })
      .mockResolvedValueOnce({ data: 'success' })

    // Start request that will trigger refresh
    const requestPromise = axiosInstance.get('/api/test')

    // Wait a bit to ensure refresh has started
    await new Promise(resolve => setTimeout(resolve, 10))

    // Resolve refresh token
    refreshResolve!({ data: { access_token: 'new-token' } })

    // Wait for request to complete
    const result = await requestPromise

    expect(result.data).toBe('success')
    expect(mockPost).toHaveBeenCalledTimes(1)
  })

  it('should handle refresh token failure gracefully', async () => {
    // Mock refresh token failure
    mockPost.mockRejectedValueOnce(new Error('Refresh failed'))

    // Mock API call that returns 401
    const mockGet = vi.spyOn(axiosInstance, 'get')
    mockGet.mockRejectedValueOnce({
      response: { status: 401 },
      config: { headers: {} }
    })

    // Make request that will trigger failed refresh
    await expect(axiosInstance.get('/api/test')).rejects.toThrow('Refresh failed')

    // Verify refresh was attempted
    expect(mockPost).toHaveBeenCalledTimes(1)
  })

  it('should not retry refresh for already retried requests', async () => {
    // Mock API call that returns 401 with _retry flag
    const mockGet = vi.spyOn(axiosInstance, 'get')
    mockGet.mockRejectedValueOnce({
      response: { status: 401 },
      config: { headers: {}, _retry: true }
    })

    // Make request that should not trigger refresh
    await expect(axiosInstance.get('/api/test')).rejects.toMatchObject({
      response: { status: 401 }
    })

    // Verify refresh was not called
    expect(mockPost).not.toHaveBeenCalled()
  })
})

/**
 * Integration test scenarios:
 * 
 * 1. Multiple simultaneous 401 errors should only trigger one refresh
 * 2. Requests should be queued during refresh and retried after
 * 3. Failed refresh should clear auth and redirect to login
 * 4. Already retried requests should not trigger another refresh
 * 5. Non-401 errors should not trigger refresh
 */

describe('useIdentityActions Refresh Logic', () => {
  it('should prevent duplicate refresh calls from hook', () => {
    // This would test the isRefreshingGlobal flag in useIdentityActions
    // but requires more complex setup with React Testing Library
    expect(true).toBe(true) // Placeholder
  })

  it('should not auto-refresh on currentUserQuery 401 errors', () => {
    // This would test that the useEffect no longer calls refreshAccessToken
    // when currentUserQuery fails with 401
    expect(true).toBe(true) // Placeholder
  })
})

/**
 * Manual testing scenarios:
 * 
 * 1. Login and wait for token to expire
 * 2. Make multiple API calls simultaneously
 * 3. Check network tab - should see only one refresh token call
 * 4. Verify all subsequent requests use the new token
 * 
 * 5. Open multiple tabs with the app
 * 6. Let token expire in one tab
 * 7. Make API calls in both tabs
 * 8. Should see coordinated refresh behavior
 */
