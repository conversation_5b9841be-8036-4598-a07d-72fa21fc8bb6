import { get<PERSON><PERSON><PERSON> } from 'hono/cookie';
import { Context, HttpStatusCode, Request, Response } from './types.js';
import { Injectable } from '../decorators/injectable.decorator.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../decorators/error-handling.decorator.js';
import {
  ForbiddenError,
  HttpError,
  NotFoundError,
  UnauthorizedError,
  ValidationError,
} from './http-error.js';
import { ValidationUtils } from '../utilities/validation.js';
import { ResponseUtils } from '../utilities/response.js';
import { ClaimsPrincipal } from '../security/claims-principal.js';
import { logger } from '@c-cam/logger';

/**
 * Base controller class that provides common functionality for all controllers
 * Includes response handling, error handling, validation, and authorization utilities
 */
@Injectable()
export abstract class ControllerBase {
  /**
   * Handles HTTP errors by converting them to appropriate responses
   * @param error The HTTP error to handle
   * @param c The request context
   * @returns A formatted error response
   */
  @ErrorHandler(HttpError)
  protected handleHttpError(error: HttpError, c: Context): Response {
    return ResponseUtils.sendError(c, error.message, error.statusCode, error.code, error.details);
  }

  /**
   * Handles validation errors with appropriate status codes and details
   * @param error The validation error to handle
   * @param c The request context
   * @returns A formatted validation error response
   */
  @ErrorHandler(ValidationError)
  protected handleValidation(error: ValidationError, c: Context): Response {
    return ResponseUtils.sendError(
      c,
      error.message,
      400 as HttpStatusCode,
      error.code,
      error.details,
    );
  }

  /**
   * Handles generic errors with logging and a generic error message
   * @param error The error to handle
   * @param c The request context
   * @returns A formatted generic error response
   */
  @ErrorHandler(Error)
  protected handleGenericError(error: Error, c: Context): Response {
    logger.error(`[Error] ${error.message}`, { stack: error.stack });
    return ResponseUtils.sendError(
      c,
      'An unexpected error occurred',
      500 as HttpStatusCode,
      'INTERNAL_SERVER_ERROR',
    );
  }

  /**
   * Creates a text response with the given status code
   * @param c The request context
   * @param text Optional text content
   * @param statusCode HTTP status code (default: 200)
   * @returns A text response
   */
  protected text(c: Context, text?: string, statusCode: HttpStatusCode = 200): Response {
    return ResponseUtils.sendText(c, text, statusCode);
  }

  /**
   * Creates a successful JSON response with the given data and status code
   * @param c The request context
   * @param data Optional data to include in the response
   * @param message Success message (default: 'Success')
   * @param statusCode HTTP status code (default: 200)
   * @returns A success response
   */
  protected success<T>(
    c: Context,
    data?: T,
    message = 'Success',
    statusCode: HttpStatusCode = 200,
  ): Response {
    return ResponseUtils.sendSuccess(c, data, message, statusCode);
  }

  /**
   * Creates a bad request response
   * @param c The request context
   * @param message Error message (default: 'Bad Request')
   * @param statusCode HTTP status code (default: 400)
   * @returns A bad request response
   */
  protected badRequest(
    c: Context,
    message = 'Bad Request',
    statusCode: HttpStatusCode = 400,
  ): Response {
    return ResponseUtils.sendError(c, message, statusCode, 'BAD_REQUEST');
  }

  /**
   * Creates an unauthorized response
   * @param c The request context
   * @param message Error message (default: 'Unauthorized')
   * @param statusCode HTTP status code (default: 401)
   * @returns An unauthorized response
   */
  protected unauthorized(
    c: Context,
    message = 'Unauthorized',
    statusCode: HttpStatusCode = 401,
  ): Response {
    return ResponseUtils.sendError(c, message, statusCode, 'UNAUTHORIZED');
  }

  /**
   * Phương thức tiện ích để tạo response thành công với dữ liệu đã được tạo
   */
  protected created<T>(c: Context, data?: T, message = 'Resource created successfully') {
    return ResponseUtils.sendCreated(c, data, message);
  }

  /**
   * Phương thức tiện ích để tạo response lỗi
   */
  protected error(
    c: Context,
    message: string,
    statusCode: HttpStatusCode = 400,
    code = 'BAD_REQUEST',
    details?: any[],
  ) {
    return ResponseUtils.sendError(c, message, statusCode, code, details);
  }

  protected notFound(c: Context, message = 'Resource not found') {
    return ResponseUtils.sendError(c, message, 404 as HttpStatusCode, 'NOT_FOUND');
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi không tìm thấy tài nguyên
   */
  protected notFoundIf(condition: boolean, message = 'Resource not found') {
    if (condition) {
      throw new NotFoundError(message);
    }
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi điều kiện xác thực không hợp lệ
   */
  protected unauthorizedIf(condition: boolean, message = 'Unauthorized access') {
    if (condition) {
      throw new UnauthorizedError(message);
    }
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi không có quyền truy cập
   */
  protected forbiddenIf(condition: boolean, message = 'Forbidden access') {
    if (condition) {
      throw new ForbiddenError(message);
    }
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi dữ liệu không hợp lệ
   */
  protected validateIf(condition: boolean, message = 'Validation failed', details?: any[]) {
    if (condition) {
      throw new ValidationError(message, details);
    }
  }

  /**
   * Validate required fields in request data
   */
  protected validateRequiredFields(data: Record<string, any>, requiredFields: string[]): void {
    ValidationUtils.validateRequiredFields(data, requiredFields);
  }

  /**
   * Sanitize request data to prevent injection attacks
   */
  protected sanitizeData<T extends Record<string, any>>(data: T): T {
    return ValidationUtils.sanitizeObject(data);
  }

  /**
   * Get the current authenticated user from the request
   * @param req Express request object
   * @returns The authenticated user or null if not authenticated
   */
  protected getCurrentUser(req: Request): any | null {
    return (req as any).user || null;
  }

  /**
   * Get the ClaimsPrincipal from the request
   * @param req Express request object
   * @returns The ClaimsPrincipal or null if not authenticated
   */
  protected getClaimsPrincipal(req: Request): ClaimsPrincipal | null {
    return (req as any).claimsPrincipal || null;
  }

  /**
   * Check if the current user has a specific claim
   * @param req Express request object
   * @param claimType The type of the claim to check
   * @param claimValue Optional value to compare against (if not provided, checks for existence)
   * @returns True if the user has the claim, false otherwise
   */
  protected userHasClaim(req: Request, claimType: string, claimValue?: string): boolean {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return false;

    return principal.hasClaim(claimType, claimValue);
  }

  /**
   * Check if the current user has any of the specified roles
   * @param req Express request object
   * @param roles Array of role names to check
   * @returns True if the user has any of the roles, false otherwise
   */
  protected userHasRole(req: Request, roles: string[]): boolean {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return false;

    return roles.some((role) => principal.isInRole(role));
  }

  /**
   * Get a claim value from the current user
   * @param req Express request object
   * @param claimType The type of the claim to get
   * @returns The claim value or undefined if not found
   */
  protected getClaimValue(req: Request, claimType: string): string | undefined {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return undefined;

    const claim = principal.findFirst(claimType);
    return claim?.value;
  }

  /**
   * Get all claim values of a specific type from the current user
   * @param req Express request object
   * @param claimType The type of the claims to get
   * @returns Array of claim values
   */
  protected getClaimValues(req: Request, claimType: string): string[] {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return [];

    return principal.findAll(claimType).map((claim) => claim.value);
  }

  /**
   * Get the user ID from the current user
   * @param req Express request object
   * @returns The user ID or undefined if not authenticated
   */
  protected getUserId(req: Request): string | undefined {
    return this.getClaimValue(req, 'sub') || this.getClaimValue(req, 'id');
  }

  /**
   * Get the user name from the current user
   * @param req Express request object
   * @returns The user name or undefined if not authenticated
   */
  protected getUserName(req: Request): string | undefined {
    const principal = this.getClaimsPrincipal(req);
    if (!principal || !principal.identity) return undefined;

    return principal.identity.name;
  }
}
