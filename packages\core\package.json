{"name": "@c-cam/core", "version": "1.0.0", "description": "Core framework for c-cam application", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist/**"], "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc --project tsconfig.app.json", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@c-cam/logger": "workspace:*", "@c-cam/shared": "workspace:*", "@c-cam/types": "workspace:*", "@hono/node-server": "^1.14.2", "bullmq": "^5.4.0", "hono": "^4.7.10", "ioredis": "^5.3.2", "kafkajs": "^2.2.4", "mongoose": "^8.15.0", "mqtt": "^5.13.0", "reflect-metadata": "^0.2.2", "winston": "^3.17.0"}, "devDependencies": {"@c-cam/eslint": "workspace:*", "@c-cam/tsconfig": "workspace:*", "@types/mongoose": "^5.11.97", "@types/node": "^18.15.11", "eslint": "^9.27.0", "rimraf": "^4.4.1", "typescript": "^5.0.4", "vitest": "^3.0.5"}}