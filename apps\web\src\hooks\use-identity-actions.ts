import { useCallback, useEffect, useRef } from 'react'
import { toast } from 'sonner'
import { useNavigate } from '@tanstack/react-router'
import { useQueryClient } from '@tanstack/react-query'
import {
  useCurrentUserQuery,
  useLoginMutation,
  useLogoutAllMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useVerifyTokenMutation,
} from './use-identity-query'
import type { LoginRequest } from '@/types/auth'
import {
  clearAccessToken,
  getAccessToken,
  getDeviceInfo,
  isTokenExpired,
  setAccessToken,
} from '@/utils/auth'

// Global flag to prevent multiple refresh attempts
let isRefreshingGlobal = false

/**
 * Hook for handling identity actions and UI interactions
 */
export const useIdentityActions = () => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const refreshTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)

  // Get current access token
  const accessToken = getAccessToken()
  const isAuthenticated = !!accessToken && !isTokenExpired(accessToken)

  // Queries and mutations
  const currentUserQuery = useCurrentUserQuery({
    enabled: isAuthenticated,
    retry: false,
    refetchOnWindowFocus: false,
  })

  const loginMutation = useLoginMutation()
  const logoutMutation = useLogoutMutation()
  const logoutAllMutation = useLogoutAllMutation()
  const refreshTokenMutation = useRefreshTokenMutation()
  const verifyTokenMutation = useVerifyTokenMutation()

  // Auto refresh token before expiry
  const scheduleTokenRefresh = useCallback((token: string) => {
    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current)
    }

    // Don't schedule if already refreshing
    if (isRefreshingGlobal) {
      return
    }

    try {
      // Parse token to get expiry time
      const tokenParts = token.split('.')
      if (tokenParts.length < 2 || !tokenParts[1]) {
        console.error('Invalid token format')
        return
      }

      const payload = JSON.parse(atob(tokenParts[1]))
      const expiryTime = payload.exp * 1000
      const currentTime = Date.now()

      // If token is already expired, don't schedule
      if (expiryTime <= currentTime) {
        console.warn('Token is already expired')
        return
      }

      // Calculate refresh time (2 minutes before expiry)
      const refreshTime = expiryTime - 2 * 60 * 1000
      const timeUntilRefresh = refreshTime - currentTime

      if (timeUntilRefresh <= 0) {
        // Token is expiring soon, but don't refresh immediately
        // Let axios interceptor handle it when needed
        console.debug('Token expiring soon, will be refreshed by interceptor when needed')
      } else {
        // Schedule refresh 2 minutes before expiry
        console.debug(`Scheduling token refresh in ${Math.round(timeUntilRefresh / 1000)} seconds`)
        refreshTimeoutRef.current = setTimeout(() => {
          // Only refresh if not already refreshing and token is still valid
          if (!isRefreshingGlobal && !isTokenExpired(getAccessToken() || '')) {
            refreshAccessToken()
          }
        }, timeUntilRefresh)
      }
    } catch (error) {
      console.error('Error scheduling token refresh:', error)
    }
  }, [])

  // Login action
  const login = useCallback(
    async (credentials: LoginRequest) => {
      try {
        const deviceInfo = getDeviceInfo()
        const response = await loginMutation.mutateAsync({
          ...credentials,
          deviceInfo,
        })

        // Store access token in memory/secure storage
        setAccessToken(response.access_token)

        // Schedule token refresh
        scheduleTokenRefresh(response.access_token)

        // Invalidate and refetch user data
        queryClient.invalidateQueries({ queryKey: ['identity', 'me'] })

        toast.success('Đăng nhập thành công!')

        // Navigate to dashboard
        navigate({ to: '/dashboard' })
      } catch (error: any) {
        const errorMessage =
          error?.response?.data?.error || 'Đăng nhập thất bại'
        toast.error(errorMessage)
        throw error
      }
    },
    [loginMutation, navigate, queryClient, scheduleTokenRefresh],
  )

  // Refresh access token action
  const refreshAccessToken = useCallback(async () => {
    // Prevent multiple simultaneous refresh attempts
    if (isRefreshingGlobal) {
      console.debug('Token refresh already in progress, skipping')
      return
    }

    try {
      isRefreshingGlobal = true
      console.debug('Starting token refresh...')

      const deviceInfo = getDeviceInfo()
      const response = await refreshTokenMutation.mutateAsync({ deviceInfo })

      // Store new access token
      setAccessToken(response.access_token)

      // Schedule next refresh
      scheduleTokenRefresh(response.access_token)

      // Invalidate queries to refetch with new token
      queryClient.invalidateQueries({ queryKey: ['identity'] })

      console.debug('Token refreshed successfully')
      return response.access_token
    } catch (error: any) {
      console.error('Token refresh failed:', error)

      // If refresh fails, logout user
      clearAccessToken()
      queryClient.clear()

      // Clear refresh timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }

      // Redirect to login
      navigate({ to: '/auth/login' })
      toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.')

      throw error
    } finally {
      isRefreshingGlobal = false
    }
  }, [refreshTokenMutation, navigate, queryClient, scheduleTokenRefresh])

  // Logout action
  const logout = useCallback(async () => {
    try {
      await logoutMutation.mutateAsync()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear local state regardless of API call result
      clearAccessToken()
      queryClient.clear()

      // Clear refresh timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }

      // Navigate to login
      navigate({ to: '/auth/login' })
      toast.success('Đăng xuất thành công!')
    }
  }, [logoutMutation, navigate, queryClient])

  // Logout from all devices action
  const logoutAll = useCallback(async () => {
    try {
      const response = await logoutAllMutation.mutateAsync()
      toast.success(`Đã đăng xuất khỏi ${response.revoked_tokens} thiết bị`)
    } catch (error) {
      console.error('Logout all error:', error)
      toast.error('Có lỗi xảy ra khi đăng xuất khỏi tất cả thiết bị')
    } finally {
      // Clear local state
      clearAccessToken()
      queryClient.clear()

      // Clear refresh timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }

      // Navigate to login
      navigate({ to: '/auth/login' })
    }
  }, [logoutAllMutation, navigate, queryClient])

  // Verify token action
  const verifyToken = useCallback(
    async (token?: string): Promise<boolean> => {
      try {
        const tokenToVerify = token || accessToken
        if (!tokenToVerify) return false

        const response = await verifyTokenMutation.mutateAsync({
          token: tokenToVerify,
        })

        return response.valid
      } catch (error) {
        console.error('Token verification error:', error)
        return false
      }
    },
    [verifyTokenMutation, accessToken],
  )

  // Initialize token refresh on mount
  useEffect(() => {
    if (accessToken && !isTokenExpired(accessToken)) {
      scheduleTokenRefresh(accessToken)
    }

    // Cleanup on unmount
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }
    }
  }, [accessToken, scheduleTokenRefresh])

  // Handle user query errors - but don't auto-refresh here to avoid duplicate calls
  // The axios interceptor will handle 401 errors and refresh tokens automatically
  useEffect(() => {
    if (currentUserQuery.error) {
      const error = currentUserQuery.error as any
      if (error?.response?.status === 401) {
        console.debug('User query failed with 401, axios interceptor will handle token refresh')
        // Don't call refreshAccessToken here to avoid duplicate refresh attempts
        // The axios interceptor will handle this automatically
      }
    }
  }, [currentUserQuery.error])

  return {
    // State
    user: currentUserQuery.data?.user || null,
    isAuthenticated,
    isLoading: currentUserQuery.isLoading || loginMutation.isPending,
    error: currentUserQuery.error || loginMutation.error,

    // Actions
    login,
    logout,
    logoutAll,
    refreshAccessToken,
    verifyToken,

    // Mutation states
    isLoggingIn: loginMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isRefreshing: refreshTokenMutation.isPending,
  }
}

export default useIdentityActions
