[2025-05-28 10:00:57] [[31<PERSON><PERSON>r[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 10:02:08] [[31<PERSON><PERSON>r[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 10:02:29] [[31<PERSON><PERSON>r[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 10:02:55] [[31mer<PERSON>r[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 10:03:16] [[31merror[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 10:04:21] [[31merror[39m] MongoServerError: cannot index parallel arrays [actions] [resources]
    at InsertOneOperation.execute (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\insert.ts:88:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async tryOperation (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\collection.ts:285:12)
[2025-05-28 10:05:30] [[31merror[39m] MongoServerError: cannot index parallel arrays [actions] [resources]
    at InsertOneOperation.execute (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\insert.ts:88:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async tryOperation (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\collection.ts:285:12)
[2025-05-28 10:05:46] [[31merror[39m] MongoServerError: cannot index parallel arrays [actions] [resources]
    at InsertOneOperation.execute (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\insert.ts:88:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async tryOperation (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (D:\Projects\csmart-cloud\c-cam-v3\v3\node_modules\.pnpm\mongodb@6.16.0_socks@2.8.4\node_modules\mongodb\src\collection.ts:285:12)
[2025-05-28 10:05:53] [[31merror[39m] TypeError: this.fixDatabaseIndexes is not a function
    at DatabaseSeeder.seed (D:\Projects\csmart-cloud\c-cam-v3\v3\apps\api\src\database\seed.ts:126:18)
    at runSeeding (D:\Projects\csmart-cloud\c-cam-v3\v3\apps\api\src\database\seed.ts:586:16)
    at initializeDatabase (D:\Projects\csmart-cloud\c-cam-v3\v3\apps\api\src\main.ts:97:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async startServer (D:\Projects\csmart-cloud\c-cam-v3\v3\apps\api\src\main.ts:115:5)
[2025-05-28 10:29:59] [[31merror[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 11:52:06] [[31merror[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 11:52:43] [[31merror[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 13:38:38] [[31merror[39m] Failed to connect to MongoDB with Mongoose
[2025-05-28 13:40:26] [[31merror[39m] Login error
[2025-05-28 13:40:27] [[31merror[39m] Login error
[2025-05-28 13:42:31] [[31merror[39m] Login error
[2025-05-28 13:42:44] [[31merror[39m] Login error
[2025-05-28 13:43:33] [[31merror[39m] Login error
[2025-05-28 13:43:41] [[31merror[39m] Login error
[2025-05-28 13:45:12] [[31merror[39m] Login error
[2025-05-28 13:45:13] [[31merror[39m] Login error
[2025-05-28 13:45:24] [[31merror[39m] Login error
[2025-05-28 13:53:13] [[31merror[39m] Login error
[2025-05-28 13:53:15] [[31merror[39m] Login error
[2025-05-28 13:53:46] [[31merror[39m] Login error
[2025-05-28 13:53:55] [[31merror[39m] Login error
[2025-05-28 13:54:23] [[31merror[39m] Login error
[2025-05-28 13:54:26] [[31merror[39m] Login error
[2025-05-28 13:54:50] [[31merror[39m] Login error
[2025-05-28 13:55:38] [[31merror[39m] Login error
[2025-05-28 13:56:35] [[31merror[39m] Login error
[2025-05-28 14:02:24] [[31merror[39m] Error creating document
[2025-05-28 14:02:35] [[31merror[39m] Login error
[2025-05-28 14:05:47] [[31merror[39m] Error creating document
[2025-05-28 14:05:47] [[31merror[39m] Login error
[2025-05-28 15:42:43] [[31merror[39m] Error creating document
[2025-05-28 15:42:43] [[31merror[39m] Refresh token error
[2025-05-28 15:42:45] [[31merror[39m] Refresh token error
[2025-05-28 15:43:36] [[31merror[39m] Error creating document
[2025-05-28 15:43:36] [[31merror[39m] Refresh token error
[2025-05-28 15:43:37] [[31merror[39m] Refresh token error
[2025-05-28 15:43:46] [[31merror[39m] Error creating document
[2025-05-28 15:43:46] [[31merror[39m] Refresh token error
[2025-05-28 15:43:47] [[31merror[39m] Refresh token error
